# 发送/停止按钮功能实现

## 功能概述

在聊天界面中实现了发送按钮到停止按钮的动态切换功能，允许用户在消息发送过程中中断操作。

## 实现的功能

### 1. 按钮状态切换
- **发送状态**: 显示发送图标和"发送"文本
- **停止状态**: 显示停止图标和"停止"文本，按钮变为红色

### 2. 停止功能
- 取消当前的流式请求
- 调用后端停止执行接口
- 重置所有相关状态

### 3. 用户体验优化
- 按钮颜色在停止模式下变为红色
- 图标有旋转动画效果
- 状态切换流畅自然

## 代码修改说明

### 1. 状态管理 (src/views/chatagent/pages/chat/index.vue)

```typescript
// 新增状态变量
const currentAbortController = ref<AbortController | null>(null)
const currentPlanId = ref<string | null>(null)
```

### 2. 按钮模板修改

```vue
<button
  :class="['send-btn', { 'stop-mode': sending }]"
  :disabled="!sending && !messageInput.trim()"
  @click="sending ? stopMessage : sendMessage"
>
  <Icon :class="sending ? 'spinning' : ''" :icon="sending ? 'carbon:stop' : 'carbon:send'" />
  <span>{{ sending ? '停止' : '发送' }}</span>
</button>
```

### 3. 停止功能实现

```typescript
const stopMessage = async () => {
  if (!sending.value) return

  try {
    // 1. 取消流式请求
    if (currentAbortController.value) {
      currentAbortController.value.abort()
    }

    // 2. 调用停止执行接口
    if (currentPlanId.value) {
      await apiServices.direct.stopExecution(currentPlanId.value)
    }

    // 3. 重置状态
    sending.value = false
    currentAbortController.value = null
    currentPlanId.value = null
  } catch (error) {
    console.error('停止消息发送失败:', error)
    // 即使出错也要重置状态
    sending.value = false
    currentAbortController.value = null
    currentPlanId.value = null
  }
}
```

### 4. DirectApiService 修改 (src/views/chatagent/api/direct-api-service.ts)

```typescript
public static async sendMessageWithStreaming(
  planTemplateId: string,
  request: DirectStreamingRequest,
  onEvent: (event: DirectStreamEvent) => void,
  onError: (error: Error) => void,
  onComplete: () => void,
  timeout: number = 5 * 60 * 1000,
  externalAbortController?: AbortController // 新增参数
): Promise<void>
```

### 5. 样式修改

```less
.send-btn {
  // 基础样式...
  
  // 停止状态的特殊样式
  &.stop-mode {
    background: var(--el-color-danger);
    
    &:hover:not(:disabled) {
      background: var(--el-color-danger-light-3);
    }
  }
}
```

## 测试步骤

### 1. 基本功能测试
1. 打开聊天界面
2. 输入一个消息
3. 点击发送按钮
4. 观察按钮是否变为红色的停止按钮
5. 点击停止按钮
6. 确认消息发送被中断，按钮恢复为发送状态

### 2. 边界情况测试
1. **快速点击**: 快速连续点击发送和停止按钮
2. **网络错误**: 在网络断开时测试停止功能
3. **长时间执行**: 测试长时间运行的任务的停止功能
4. **重复操作**: 多次发送和停止操作

### 3. 用户体验测试
1. 按钮颜色变化是否明显
2. 图标切换是否流畅
3. 文本变化是否清晰
4. 停止操作是否及时响应

## 技术要点

### 1. AbortController 使用
- 用于取消 fetch 请求
- 支持外部传入，便于统一管理
- 确保请求能够及时中断

### 2. 状态管理
- `sending`: 控制按钮状态和禁用逻辑
- `currentAbortController`: 管理当前请求的取消控制器
- `currentPlanId`: 存储当前执行的计划ID，用于调用停止接口

### 3. 错误处理
- 停止操作失败时仍然重置状态
- 避免状态不一致导致的界面问题
- 提供适当的错误日志

### 4. API 集成
- 使用新的 `apiServices.direct.stopExecution` 接口
- 兼容现有的流式请求处理逻辑
- 支持向后兼容

## 注意事项

1. **状态一致性**: 确保所有相关状态在停止时都被正确重置
2. **错误处理**: 即使停止接口调用失败，也要重置UI状态
3. **用户反馈**: 提供清晰的视觉反馈表明操作状态
4. **性能考虑**: 避免频繁的状态切换影响性能

## 后续优化建议

1. **进度显示**: 可以考虑添加更详细的进度指示
2. **确认对话框**: 对于重要操作可以添加确认提示
3. **快捷键支持**: 支持 Ctrl+C 等快捷键停止操作
4. **状态持久化**: 考虑在页面刷新后恢复状态
