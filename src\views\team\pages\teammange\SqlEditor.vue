<template>
  <div class="sql-editor-container">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button type="primary" size="small" @click="executeSql">
            <el-icon><VideoPlay /></el-icon>
            执行
          </el-button>
          <el-button size="small" @click="formatSql">
            <el-icon><Operation /></el-icon>
            格式化
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <!-- Monaco编辑器 -->
    <div ref="editorContainer" class="monaco-editor-container"></div>
    
    <!-- SQL执行结果 -->
    <div v-if="showResult" class="sql-result-container">
      <div v-if="loading" class="sql-loading">
        <el-icon class="loading"><Loading /></el-icon> 正在执行...
      </div>
      <div v-else-if="error" class="sql-error">
        执行失败: {{ error }}
      </div>
      <template v-else>
        <el-tabs v-model="activeTab">
          <!-- <el-tab-pane label="总览" name="summary">
            <div class="sql-summary">
              执行成功: {{ result.time || 0 }}ms, {{ result.rows || 0 }}行
            </div>
          </el-tab-pane> -->
          <el-tab-pane label="结果" name="result">
            <div v-if="result && result.data && result.data.length > 0">
              <VTable 
                :data="result.data" 
                :columns="tableColumns"
              />
            </div>
            <div v-else class="sql-empty">查询结果为空</div>
          </el-tab-pane>
        </el-tabs>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { VideoPlay, Operation, DocumentAdd, Download, DataLine, Loading } from '@element-plus/icons-vue';
import * as monaco from 'monaco-editor';
import VTable from './VTable.vue';

// 编辑器引用
const editorContainer = ref(null);
let editor = null;

// 数据源相关
const selectedDataSource = ref('');
const selectedDatabase = ref('');
const selectedSchema = ref('');
const databases = ref([]);
const schemas = ref([]);

// SQL执行结果相关
const showResult = ref(false);
const loading = ref(false);
const error = ref(null);
const result = ref(null);
const activeTab = ref('result');
const tableColumns = ref([]);

// 定义props
const props = defineProps({
  value: {
    type: String,
    default: 'SELECT * FROM employees LIMIT 10;'
  }
});

// 初始化编辑器
onMounted(() => {
  if (editorContainer.value) {
    editor = monaco.editor.create(editorContainer.value, {
      value: props.value,
      language: 'sql',
      theme: 'vs',
      automaticLayout: true,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      fontSize: 14,
      tabSize: 2
    });
  }
});

// 监听value属性变化
watch(() => props.value, (newValue) => {
  if (editor && newValue) {
    // 只有当编辑器的值与新值不同时才更新
    if (editor.getValue() !== newValue) {
      editor.setValue(newValue);
    }
  }
});

// 组件销毁时处理
onBeforeUnmount(() => {
  if (editor) {
    editor.dispose();
  }
});

// 执行SQL
const executeSql = async () => {
  if (!editor) return;
  
  const sql = editor.getValue();
  if (!sql.trim()) {
    ElMessage.warning('SQL语句不能为空');
    return;
  }
  
  // 重置状态
  showResult.value = true;
  loading.value = true;
  error.value = null;
  result.value = null;
  
  try {
    // 模拟API调用
    // 实际项目中应该调用后端API
    setTimeout(() => {
      // 模拟数据
      const mockResult = {
        time: 123,
        rows: 5,
        data: [
          { id: 1, name: '张三', department: '技术部', salary: 15000 },
          { id: 2, name: '李四', department: '市场部', salary: 12000 },
          { id: 3, name: '王五', department: '财务部', salary: 13000 },
          { id: 4, name: '赵六', department: '人事部', salary: 10000 },
          { id: 5, name: '钱七', department: '技术部', salary: 16000 }
        ]
      };
      
      result.value = mockResult;
      
      // 提取表格列
      if (mockResult.data.length > 0) {
        tableColumns.value = Object.keys(mockResult.data[0]);
      }
      
      loading.value = false;
    }, 1000);
  } catch (err) {
    error.value = err.toString();
    loading.value = false;
  }
};

// 格式化SQL
const formatSql = () => {
  if (!editor) return;
  
  // 获取当前SQL
  const sql = editor.getValue();
  if (!sql.trim()) return;
  
  // 这里应该使用专业的SQL格式化库
  // 这里只是简单示例
  const formattedSql = sql
    .replace(/\s+/g, ' ')
    .replace(/\s*,\s*/g, ', ')
    .replace(/\s*=\s*/g, ' = ')
    .replace(/\s*>\s*/g, ' > ')
    .replace(/\s*<\s*/g, ' < ')
    .replace(/\s*\(\s*/g, ' (')
    .replace(/\s*\)\s*/g, ') ')
    .replace(/\bSELECT\b/gi, 'SELECT')
    .replace(/\bFROM\b/gi, '\nFROM')
    .replace(/\bWHERE\b/gi, '\nWHERE')
    .replace(/\bGROUP BY\b/gi, '\nGROUP BY')
    .replace(/\bHAVING\b/gi, '\nHAVING')
    .replace(/\bORDER BY\b/gi, '\nORDER BY')
    .replace(/\bLIMIT\b/gi, '\nLIMIT');
  
  // 更新编辑器内容
  editor.setValue(formattedSql);
  ElMessage.success('SQL已格式化');
};

// 保存SQL
const saveSql = () => {
  if (!editor) return;
  
  const sql = editor.getValue();
  if (!sql.trim()) {
    ElMessage.warning('SQL语句不能为空');
    return;
  }
  
  // 这里应该调用保存SQL的API
  ElMessage.success('SQL已保存');
};

// 另存为文件
const saveAsFile = () => {
  if (!editor) return;
  
  const sql = editor.getValue();
  if (!sql.trim()) {
    ElMessage.warning('SQL语句不能为空');
    return;
  }
  
  // 创建Blob对象
  const blob = new Blob([sql], { type: 'text/plain' });
  
  // 创建下载链接
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `query_${new Date().getTime()}.sql`;
  
  // 触发下载
  document.body.appendChild(link);
  link.click();
  
  // 清理
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
  
  ElMessage.success('SQL文件已下载');
};

// 数据源变更处理
const onDataSourceChange = () => {
  // 重置数据库和schema选择
  selectedDatabase.value = '';
  selectedSchema.value = '';
  schemas.value = [];
  
  // 模拟加载数据库列表
  databases.value = [
    { label: 'MySQL数据库', value: 'mysql' },
    { label: 'PostgreSQL数据库', value: 'postgres' },
    { label: 'Oracle数据库', value: 'oracle' }
  ];
};

// 数据库变更处理
const onDatabaseChange = () => {
  // 重置schema选择
  selectedSchema.value = '';
  
  // 模拟加载schema列表
  schemas.value = [
    { label: 'public', value: 'public' },
    { label: 'dbo', value: 'dbo' },
    { label: 'information_schema', value: 'information_schema' }
  ];
};
</script>

<style scoped>
.sql-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e6e6e6;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.monaco-editor-container {
  flex: 1;
  min-height: 300px;
}

.sql-result-container {
  margin-top: 10px;
  border-top: 1px solid #e6e6e6;
  max-height: 300px;
  overflow: auto;
  padding: 8px;
}

.sql-loading {
  padding: 16px;
  text-align: center;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.sql-error {
  padding: 16px;
  color: #f56c6c;
  background-color: #fef0f0;
}

.sql-summary {
  padding: 16px;
  color: #67c23a;
  background-color: #f0f9eb;
}

.sql-empty {
  padding: 16px;
  text-align: center;
  color: #909399;
}
</style>