// 多系列图表测试用例
// 这个文件展示了如何使用新的多系列图表功能

// 测试数据 - 新的多系列格式 (折线图)
const multiSeriesLineData = {
  chartType: "line",
  select_data: [
    {
      date: "2023-01-01",
      total_mails: 100,
      overrated_mails: 20,
      overrated_24_hours: 5
    },
    {
      date: "2023-01-02",
      total_mails: 120,
      overrated_mails: 25,
      overrated_24_hours: 8
    },
    {
      date: "2023-01-03",
      total_mails: 90,
      overrated_mails: 15,
      overrated_24_hours: 3
    },
    {
      date: "2023-01-04",
      total_mails: 150,
      overrated_mails: 30,
      overrated_24_hours: 12
    },
    {
      date: "2023-01-05",
      total_mails: 110,
      overrated_mails: 22,
      overrated_24_hours: 7
    }
  ],
  XFields: "date",
  series: {
    dataFields: [
      "total_mails",
      "overrated_mails",
      "overrated_24_hours"
    ]
  },
  title: "邮件统计多维度分析"
}

// 测试数据 - 多系列饼图
const multiSeriesPieData = {
  chartType: "pie",
  select_data: [
    {
      category: "产品A",
      sales: 1200,
      profit: 300
    },
    {
      category: "产品B",
      sales: 800,
      profit: 200
    },
    {
      category: "产品C",
      sales: 1500,
      profit: 450
    }
  ],
  XFields: "category",
  series: {
    dataFields: ["sales", "profit"]
  },
  title: "产品销售与利润分析"
}

// 测试数据 - 兼容原有单系列格式 (将被转换为series.dataFields)
const legacySingleSeriesData = {
  chartType: "line",
  select_data: [
    { date: "2023-01-01", total_mails: 100 },
    { date: "2023-01-02", total_mails: 120 },
    { date: "2023-01-03", total_mails: 90 },
    { date: "2023-01-04", total_mails: 150 },
    { date: "2023-01-05", total_mails: 110 }
  ],
  XFields: "date",
  YFields: "total_mails", // 这将被转换为 series: { dataFields: ["total_mails"] }
  title: "邮件总数趋势 (兼容格式)"
}

// 测试数据 - 新格式单系列
const newSingleSeriesData = {
  chartType: "line",
  select_data: [
    { date: "2023-01-01", total_mails: 100 },
    { date: "2023-01-02", total_mails: 120 },
    { date: "2023-01-03", total_mails: 90 },
    { date: "2023-01-04", total_mails: 150 },
    { date: "2023-01-05", total_mails: 110 }
  ],
  XFields: "date",
  series: {
    dataFields: ["total_mails"]
  },
  title: "邮件总数趋势 (新格式)"
}

// 测试数据 - 柱状图多系列
const barMultiSeriesData = {
  chartType: "bar",
  select_data: [
    {
      category: "产品A",
      sales: 1200,
      profit: 300,
      cost: 900
    },
    {
      category: "产品B",
      sales: 800,
      profit: 200,
      cost: 600
    },
    {
      category: "产品C",
      sales: 1500,
      profit: 450,
      cost: 1050
    }
  ],
  XFields: "category",
  series: {
    dataFields: ["sales", "profit", "cost"]
  },
  title: "产品销售分析"
}

// 模拟测试函数
function testMultiSeriesChart() {
  console.log("=== 测试多系列图表功能 ===")

  // 测试1: 多系列折线图
  console.log("\n1. 测试多系列折线图:")
  const { data, chartType, xFields, series } = multiSeriesLineData
  const dataFields = series && series.dataFields ? series.dataFields : []
  console.log("数据字段:", dataFields)
  console.log("预期系列数量:", dataFields.length)

  // 测试2: 多系列饼图
  console.log("\n2. 测试多系列饼图:")
  const pieFields = multiSeriesPieData.series.dataFields
  console.log("饼图数据字段:", pieFields)
  console.log("预期饼图系列数量:", pieFields.length)

  // 测试3: 兼容性测试
  console.log("\n3. 测试兼容性转换:")
  console.log("原YFields格式:", legacySingleSeriesData.YFields)
  console.log("应转换为:", { dataFields: [legacySingleSeriesData.YFields] })

  // 模拟颜色分配
  const colors = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
  ]

  console.log("\n4. 颜色分配测试:")
  dataFields.forEach((field, index) => {
    console.log(`系列 ${index + 1}: ${field} - 颜色: ${colors[index % colors.length]}`)
  })

  return true
}

// 测试数据转换函数
function testDataConversion() {
  console.log("\n=== 测试数据转换 ===")

  // 模拟fetchChartDataFromTool的转换逻辑
  function convertToNewFormat(toolResult) {
    const chartItem = {
      toolName: "test_chart",
      stepId: "test-123",
      chartType: toolResult.chartType || 'line',
      data: toolResult.select_data || [],
      xFields: toolResult.XFields,
      title: "测试图表"
    }

    // 优先使用series.dataFields
    if (toolResult.series && toolResult.series.dataFields && Array.isArray(toolResult.series.dataFields)) {
      chartItem.series = toolResult.series
    } else if (toolResult.YFields) {
      // 兼容性转换
      const yFieldsValue = toolResult.YFields
      const dataFields = Array.isArray(yFieldsValue) ? yFieldsValue : [yFieldsValue]
      chartItem.series = {
        dataFields: dataFields
      }
      chartItem.yFields = yFieldsValue // 保留用于兼容
    }

    return chartItem
  }

  // 测试新格式
  console.log("新格式转换结果:")
  console.log(JSON.stringify(convertToNewFormat(multiSeriesLineData), null, 2))

  // 测试兼容格式
  console.log("\n兼容格式转换结果:")
  console.log(JSON.stringify(convertToNewFormat(legacySingleSeriesData), null, 2))
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境 - 导出模块并运行测试
  module.exports = {
    multiSeriesLineData,
    multiSeriesPieData,
    legacySingleSeriesData,
    newSingleSeriesData,
    barMultiSeriesData,
    testMultiSeriesChart,
    testDataConversion
  }

  // 在Node.js环境下也运行测试
  console.log("多系列图表测试数据已准备就绪")
  testMultiSeriesChart()
  testDataConversion()
} else {
  // 浏览器环境
  console.log("多系列图表测试数据已准备就绪")
  testMultiSeriesChart()
  testDataConversion()
}
