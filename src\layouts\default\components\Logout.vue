<template>
  <div class="flex items-center">
    <label class="btn btn-ghost btn-circle cpvf-avatar">
      <span class="w-10 rounded-full">
        <el-avatar
          class="w-16 h-16"
          style="width: 30px; height: 30px; margin: 12px 0"
          :src="userStore.getUserInfo.avatarPath || '/resource/img/avatar.png'"
        />
      </span>
    </label>
    <el-dropdown trigger="click" class="pl-2 pr-8">
      <span class="text-base flex items-center">
        {{ userStore.getUserInfo.realName }}
        <sys-icon class="ml-2" type="ArrowDown" />
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="userInfo()">{{
            transformI18n(i18nType + 'userInfo', isuseI18n)
          }}</el-dropdown-item>
          <el-dropdown-item @click="logout()">{{
            transformI18n(i18nType + 'hsLoginOut', isuseI18n)
          }}</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script lang="ts" setup name="DefaultSide">
  import { transformI18n } from '/@/utils/i18n';
  import { useUserStore } from '/@/stores/modules/user';
  import router from '/@/router';
  import { useLogout } from '/@/hooks/web/useLogout';
  const i18nType = 'buttons.';
  const isuseI18n = true;
  const userStore = useUserStore();
  const { logout } = useLogout();
  const userInfo = () => {
    router.push({ path: '/user/personalcenter' });
  };
</script>
