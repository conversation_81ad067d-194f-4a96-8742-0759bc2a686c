<template>
  <div class="user-container">
    <div class="user-header">
      <h3>用户管理</h3>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form
        :inline="true"
        :model="searchParams"
        class="demo-form-inline"
        label-width="60"
      >
        <el-form-item label="用户名">
          <el-input
            v-model="searchParams.username"
            placeholder="请输入用户名"
            clearable
          />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="searchParams.name" placeholder="请输入姓名" clearable />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchParams.phone" placeholder="请输入手机号" clearable />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="searchParams.email" placeholder="请输入邮箱" clearable />
        </el-form-item>
        <el-form-item label="性别">
          <el-select
            v-model="searchParams.sex"
            placeholder="请选择性别"
            clearable
            style="width: 210px"
          >
            <el-option label="全部" value="" />
            <el-option label="女" value="0" />
            <el-option label="男" value="1" />
            <el-option label="未知" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchParams.enabled"
            placeholder="请选择状态"
            clearable
            style="width: 210px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" :icon="Plus"> 新增用户 </el-button>
      <el-button
        type="danger"
        @click="handleBatchDelete"
        :icon="Delete"
        :disabled="selectedIds.length === 0"
      >
        批量删除
      </el-button>
      <el-button @click="handleRefresh" :icon="Refresh"> 刷新 </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        stripe
        @selection-change="handleSelectionChange"
        border
        height="calc(100vh - 460px)"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column
          prop="username"
          label="用户名"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="name"
          label="姓名"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="phone"
          label="手机号"
          min-width="130"
          show-overflow-tooltip
        />
        <el-table-column
          prop="email"
          label="邮箱"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column prop="sex" label="性别" width="80" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.sex === '0'" type="danger" size="small">女</el-tag>
            <el-tag v-else-if="row.sex === '1'" type="primary" size="small">男</el-tag>
            <el-tag v-else type="info" size="small">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enabled" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.enabled === '1'" type="success" size="small">启用</el-tag>
            <el-tag v-else type="danger" size="small">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="loginIp"
          label="最后登录IP"
          min-width="120"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="loginTime"
          label="最后登录时间"
          width="160"
          align="center"
        />
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="160"
          align="center"
        />
        <el-table-column label="操作" fixed="right" width="280" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)"> 编辑 </el-button>
            <el-button type="primary" link @click="resetPwdClick(row)"
              >重置密码</el-button
            >
            <el-button type="primary" link @click="handleAssignRoles(row)">
              分配角色
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 用户编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="formData.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="formData.sex" placeholder="请选择性别">
            <el-option label="男" value="1" />
            <el-option label="女" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="enabled">
          <el-select v-model="formData.enabled" placeholder="请选择状态">
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 分配角色对话框 -->
    <el-dialog
      v-model="roleDialogVisible"
      title="分配角色"
      width="700px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-transfer
        v-model="selectedRoleIds"
        :data="roleOptions"
        :titles="['未拥有角色', '已拥有角色']"
        :button-texts="['取消', '添加']"
        :props="{ key: 'id', label: 'name' }"
        filterable
        filter-placeholder="请输入角色名称"
        style="text-align: left; display: inline-block"
      />
      <template #footer>
        <el-button @click="roleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitRoles" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <el-dialog v-model="visibleResetPwd" title="重置密码" width="400px">
      <basic-form
        class="reset-password"
        :formList="passwordFormSchema"
        :isCreate="false"
        :formData="formDataResetPwd"
        :showSubmit="false"
        ref="formResetPws"
      />
      <template #footer>
        <el-button @click="visibleResetPwd = false">取消</el-button>
        <el-button type="primary" @click="resetPwd">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Plus, Refresh, Delete } from '@element-plus/icons-vue';
  import BasicForm from '/@/components/sys/BasicForm';
  import {
    getUserPageList,
    saveUser,
    updateUser,
    deleteUsers,
    type UserInfo,
    type UserQueryParams
  } from '/@/api/sys/users';
  import { AesEncryption, cacheCipher } from '/@/utils/cipher';
  // 响应式数据
  const tableRef = ref();
  const formRef = ref();

  const tableData = ref<UserInfo[]>([]);
  const loading = ref(false);
  const submitLoading = ref(false);
  const selectedIds = ref<string[]>([]);

  // 角色分配相关
  const roleDialogVisible = ref(false);
  const selectedRoleIds = ref<string[]>([]);
  const roleOptions = ref<any[]>([]);
  const currentUserId = ref<string>('');

  // 分页数据
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const formResetPws = ref<HTMLDivElement | null>(null);
    const encryption = new AesEncryption(cacheCipher);
  // 对话框状态
  const dialogVisible = ref(false);
  const dialogTitle = ref('');
  const isEdit = ref(false);
  const visibleResetPwd = ref(false);
  // 表单数据
  const formData = ref<UserInfo>({
    username: '',
    name: '',
    phone: '',
    email: '',
    sex: '1',
    enabled: '1',
  });

  // 表单验证规则
  const formRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' }
    ],
    enabled: [
      { required: true, message: '请选择状态', trigger: 'change' }
    ]
  };
  const passwordFormSchema = [
    {
      field: 'username',
      label: '账号名：',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      required: true,
    },
    {
      label: '默认密码：',
      field: 'password',
      component: 'Input',
      required: true,
    },
  ];
  const formDataResetPwd = ref<any>({
    id: '',
    username: '',
    sendEmail: [],
    sendSMS: [],
    password: '',
  });
  // 搜索参数
  const searchParams = ref<UserQueryParams>({});

  // 表格选择变化
  const handleSelectionChange = (selection: UserInfo[]) => {
    selectedIds.value = selection.map(item => item.id!);
  };

  // 生命周期
  onMounted(() => {
    loadUserList();
  });

  // 加载用户列表
  const loadUserList = async () => {
    try {
      loading.value = true;
      const params = {
        ...searchParams.value,
        currentPage: pagination.current,
        pageSize: pagination.pageSize,
      };

      const result = await getUserPageList(params);
      if (result && result.data) {
        tableData.value = result.data;
        pagination.total = result.total;
      }
    } catch (error) {
      ElMessage.error('加载用户列表失败');
      console.error('Load user list error:', error);
    } finally {
      loading.value = false;
    }
  };

  // 搜索处理
  const handleSearch = () => {
    pagination.current = 1;
    loadUserList();
  };

  // 重置搜索
  const handleReset = () => {
    searchParams.value = {};
    pagination.current = 1;
    loadUserList();
  };

  // 刷新
  const handleRefresh = () => {
    loadUserList();
  };

  // 分页处理
  const handlePageChange = (page: number) => {
    pagination.current = page;
    loadUserList();
  };

  const handleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.current = 1;
    loadUserList();
  };

  // 新增用户
  const handleAdd = () => {
    dialogTitle.value = '新增用户';
    isEdit.value = false;
    formData.value = {
      username: '',
      name: '',
      phone: '',
      email: '',
      sex: '1',
      enabled: '1',
    };
    dialogVisible.value = true;
  };

  // 编辑用户
  const handleEdit = (record: UserInfo) => {
    dialogTitle.value = '编辑用户';
    isEdit.value = true;
    formData.value = { ...record };
    dialogVisible.value = true;
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const valid = await formRef.value?.validate();
      if (!valid) return;

      submitLoading.value = true;

      if (isEdit.value) {
        await updateUser(formData.value);
        ElMessage.success('更新用户成功');
      } else {
        await saveUser(formData.value);
        ElMessage.success('新增用户成功');
      }

      dialogVisible.value = false;
      loadUserList();
    } catch (error) {
      ElMessage.error(isEdit.value ? '更新用户失败' : '新增用户失败');
      console.error('Submit user error:', error);
    } finally {
      submitLoading.value = false;
    }
  };

  // 删除用户
  const handleDelete = async (record: UserInfo) => {
    try {
      await ElMessageBox.confirm('确定要删除该用户吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      await deleteUsers(record.id!);
      ElMessage.success('删除用户成功');
      loadUserList();
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除用户失败');
        console.error('Delete user error:', error);
      }
    }
  };

  // 批量删除用户
  const handleBatchDelete = async () => {
    try {
      await ElMessageBox.confirm(`确定要删除选中的 ${selectedIds.value.length} 个用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      await deleteUsers(selectedIds.value.join(','));
      ElMessage.success('批量删除用户成功');
      selectedIds.value = [];
      loadUserList();
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('批量删除用户失败');
        console.error('Batch delete users error:', error);
      }
    }
  };

  // 分配角色
  const handleAssignRoles = async (record: UserInfo) => {
    try {
      currentUserId.value = record.id!;
      // TODO: 加载所有角色和用户已有角色
      // const allRoles = await getAllRoles();
      // const userRoles = await getUserRoles(record.id!);

      // 模拟数据，实际应该从API获取
      roleOptions.value = [
        { id: '1', name: 'admin', key: '1' },
        { id: '2', name: 'user', key: '2' },
      ];
      selectedRoleIds.value = ['1']; // 模拟用户已有角色

      roleDialogVisible.value = true;
    } catch (error) {
      ElMessage.error('加载角色信息失败');
      console.error('Load roles error:', error);
    }
  };

  // 提交角色分配
  const handleSubmitRoles = async () => {
    try {
      submitLoading.value = true;
      // TODO: 调用分配角色API
      // await assignUserRoles(currentUserId.value, selectedRoleIds.value);

      ElMessage.success('分配角色成功');
      roleDialogVisible.value = false;
    } catch (error) {
      ElMessage.error('分配角色失败');
      console.error('Assign roles error:', error);
    } finally {
      submitLoading.value = false;
    }
  };


  const resetPwdClick = (row) => {
    console.log(row);
    visibleResetPwd.value = true;
    const { id, username } = row;
    formDataResetPwd.value = {
      id,
      username: username ? username : '',
      sendEmail: [],
      sendSMS: [],
      // userPasswd: getDictStorage().biz_ori_pass[0].value,
      password: 'Cpvf_upms@2025'
    };
  };
  /**
   * 重置密码
   */
  const resetPwd = () => {
    const getData = formResetPws.value?.submitForm;
    const ruleFormRef = formResetPws.value?.ruleFormRef;
    getData(ruleFormRef, (res, data) => {
      // data.value.deptId = deptId;
      const { id, password } = formDataResetPwd.value;
      const { username, sendEmail, sendSMS } = data;
      const params = {
        id,
        username,
        sendEmail: sendEmail.length > 0 ? true : false,
        sendSMS: sendSMS.length > 0 ? true : false,
        password: encryption.pwdEncryptByAES(password),
      };
      if (res == 'success') {
        console.log(params);
        // resetPsssword(params).then(() => {
        //   visibleResetPwd.value = false;
        //   accountList();
        //   ElMessage({
        //     type: 'success',
        //     message: '重置密码成功',
        //   });
        // });
      }
    });
  };
</script>

<style scoped lang="scss">
  .user-container {
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .user-header {
      margin-bottom: 24px;
      padding-bottom: 12px;
      border-bottom: 2px solid #f0f2f5;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .search-section {
      margin-bottom: 20px;
      // padding: 20px;
      // background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      // border-radius: 8px;
      // border: 1px solid #e2e8f0;
    }

    .action-section {
      margin-bottom: 20px;
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .table-section {
      background: #fff;
      border-radius: 8px;
      overflow: hidden;

      .pagination-section {
        margin-top: 20px;
        padding: 16px 0;
        display: flex;
        justify-content: flex-end;
        // background: #fafbfc;
        // border-top: 1px solid #e2e8f0;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }
</style>
