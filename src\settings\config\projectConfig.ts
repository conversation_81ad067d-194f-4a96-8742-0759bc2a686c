import { CacheTypeEnum } from '/@/enums/cacheEnum';
import {
  PermissionModeEnum,
  I18nTypeEnum,
  Layout,
  VerifyCodeEnum,
  SettingsEnum,
  LoginEnum,
} from '/@/enums/appEnum';
import config from '/@/styles/custom-variables.module.scss';

export const projectSettings: ProjectConfigs = {
  // 该文件采用优先缓存读取还是从文件读取
  settingConfig: SettingsEnum.FILE,
  // 缓存模式
  cacheType: CacheTypeEnum.LOCAL,
  // 权限形式
  permissionMode: PermissionModeEnum.ROUTE_MAPPING,
  // 布局 Layout.HORIZONTAL 横向菜单 Layout.VERTICAL 左侧菜单
  layout: Layout.VERTICAL,
  // Menu config
  menuSetting: {
    // 是否折叠
    collapsed: true,
  },
  // 菜单宽度
  menuWidth: 200,
  // 折叠后的宽度
  collapseWidth: 60,
  // 国际化 I18nTypeEnum.zh I18nTypeEnum.en I18nTypeEnum.none
  locale: I18nTypeEnum.zh,
  // 主题 使用 element-plus
  theme: {
    // main: 'rgba(205, 208, 220, 0.8)',
    main: '#1a5efe',
    nav: '#fff',
    menu: '#f9fafd',
    content: '#f6f8f9',
  },
  // 是否包含upms
  upms: true,
  // 缓存前缀，微前端集成时，区分不同应用
  storageName: 'CPVF_',
  // class 前缀
  prefixCls: (config as any).namespace,
  // verifyCode VerifyCodeEnum.BOTH VerifyCodeEnum.FRONT VerifyCodeEnum.BACKEND VerifyCodeEnum.NONE VerifyCodeEnum.SLIDER
  verifyCode: VerifyCodeEnum.BOTH,
  // 是否启用二维码
  loginCode: false,
  // 0：不使用统一认证，1：使用统一认证
  loginType: 1,
  // 是否使用短信登录
  loginMsg: false,
  // 登录页面使用的登录方式, 至少存在一种登录方式
  // loginTypes: [LoginEnum.USER, LoginEnum.QrCode, LoginEnum.MSG],
  loginTypes: [LoginEnum.USER],
  // 是否启用统一认证的登录
  useLoginPage: false,
  // 登录页面 图片验证方式，oss为true  走后台请求图片，false本地图片验证
  oss: false,
  // 生产环境是否缓存加密，如果开启加密，发版后，首次加载需要提示用户清除缓存
  isEncrypt: true,
  // 与isEncrypt同为true时，可解决由于内存加密后，首次读取后报错，需要清空缓存的问题，会在捕捉到缓存错误时，自动清空并返回登录页
  encryptClearStorage: true,
  // 是否启用 tab页签功能
  useTab: true,
};
