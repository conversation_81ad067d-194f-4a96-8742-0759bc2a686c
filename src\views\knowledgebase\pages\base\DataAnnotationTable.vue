<template>
  <div class="data-annotation-container" ref="containerRef">
    <!-- 左侧数据源树 -->
    <div class="left-panel" :style="{ width: leftPanelWidth + 'px' }">
      <div class="panel-header">
        <h3>数据库</h3>
        <!-- 实现刷新功能 -->
        <el-icon style="cursor:pointer;" @click="refreshTree">
          <component :is="Refresh" />
        </el-icon>
      </div>
      <div class="tree-container">
        <el-tree
          v-loading="treeLoading"
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :load="loadNode"
          lazy
          node-key="id"
          :expand-on-click-node="true"
          :highlight-current="true"
          :current-node-key="currentNodeKey"
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <span class="tree-node">
              <Iconfont
                v-if="data?.sqlType && getDatabaseIcon(data.sqlType)"
                class="node-icon-iconfont"
                :name="getDatabaseIcon(data.sqlType) || ''"
              />
              
              <el-icon  v-else class="node-icon">
                <!-- {{ data.type }} -->
                <component v-if="data.type == 'database'" color="#337ecc" :is="getNodeIcon(data.type)" />
                <component v-if="data.type == 'table'" color="#529b2e" :is="getNodeIcon(data.type)" />
                <component v-if="data.type == 'schema'" color="#b88230" :is="getNodeIcon(data.type)" />
              </el-icon>
              <span class="node-label">{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 分割线 -->
    <div
      class="splitter"
      :class="{ 'splitter-resizing': isResizing }"
      @mousedown="startResize"
    ></div>

    <!-- 右侧数据标注表格 -->
    <div class="right-panel" :style="{ width: rightPanelWidth + 'px' }">
      <div class="panel-header">
        <h3>{{ selectedTableName ? `${selectedTableName}` : 'AI注释' }}</h3>
        <!-- <h3 style="min-width: 50px;">AI注释</h3> -->
        <!-- 补足表注释 -->
        <el-input
          v-if="selectedTableName"
          v-model="tableCommentAlias"
          placeholder="请输入表AI注释"
          style="margin: 0 16px;"
          clearable
        />
        <div class="header-actions" v-if="selectedTableName">
          <el-button 
            type="primary" 
            :disabled="!hasChanges"
            @click="saveAnnotations"
          >
            保存
          </el-button>
          <el-button 
            type="success"
            @click="syncAnnotations"
          >
            同步
          </el-button>
          <el-button @click="refreshAnnotations">刷新</el-button>
        </div>
      </div>
      
      <div class="table-container">
        <el-table
          ref="tableRef"
          :data="tableColumns"
          v-loading="loading"
          border
          @cell-dblclick="handleCellEdit"
        >
          <el-table-column prop="columnName" label="原列名(注释)" min-width="200">
            <template #default="{ row }">
              <div class="column-info">
                <div class="column-name">
                  {{ row.columnName }}
                  <span class="column-comment" v-if="row.originalComment">
                  ({{ row.originalComment }})
                </span>
                </div>
                
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="columnCommentAlias" label="自定义AI列注释" min-width="200">
            <template #default="{ row, $index }">
              <div v-if="row.isEditing && editingCell.rowIndex === $index && editingCell.column === 'columnCommentAlias'">
                <el-input
                  ref="editInputRef"
                  v-model="row.columnCommentAlias"
                  @blur="handleCellSave(row, $index, 'columnCommentAlias')"
                  @keyup.enter="handleCellSave(row, $index, 'columnCommentAlias')"
                  @keyup.esc="handleCellCancel(row, $index)"
                  
                  placeholder=""
                />
              </div>
              <div v-else class="editable-cell" @dblclick="handleCellEdit(row, null, null, $index, 'columnCommentAlias')">
                <span v-if="row.columnCommentAlias" class="cell-content">{{ row.columnCommentAlias }}</span>
                <span v-else class="cell-placeholder">双击编辑</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="functionExamples" label="示例函数" min-width="120" align="center">
            <template #default="{ row, $index }">
              <div v-if="row.isEditing && editingCell.rowIndex === $index && editingCell.column === 'functionExamples'">
                <el-input
                  ref="editInputRef"
                  v-model="row.functionExamples"
                  @blur="handleCellSave(row, $index, 'functionExamples')"
                  @keyup.enter="handleCellSave(row, $index, 'functionExamples')"
                  @keyup.esc="handleCellCancel(row, $index)"
                  
                  placeholder=""
                />
              </div>
              <div v-else class="editable-cell" @dblclick="handleCellEdit(row, null, null, $index, 'functionExamples')">
                <span v-if="row.functionExamples" class="cell-content">{{ row.functionExamples }}</span>
                <span v-else class="cell-placeholder">双击编辑</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="columnExampleData" label="示例数据" min-width="120" align="center">
            <template #default="{ row, $index }">
              <div v-if="row.isEditing && editingCell.rowIndex === $index && editingCell.column === 'columnExampleData'">
                <el-input
                  ref="editInputRef"
                  v-model="row.columnExampleData"
                  @blur="handleCellSave(row, $index, 'columnExampleData')"
                  @keyup.enter="handleCellSave(row, $index, 'columnExampleData')"
                  @keyup.esc="handleCellCancel(row, $index)"
                  placeholder=""
                />
              </div>
              <div v-else class="editable-cell" @dblclick="handleCellEdit(row, null, null, $index, 'columnExampleData')">
                <span v-if="row.columnExampleData" class="cell-content">{{ row.columnExampleData }}</span>
                <span v-else class="cell-placeholder">双击编辑</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="foreignTableName" label="关联外键表名" min-width="150" align="center">
            <template #default="{ row, $index }">
              <div v-if="row.isEditing && editingCell.rowIndex === $index && editingCell.column === 'foreignTableName'">
                <el-input
                  ref="editInputRef"
                  v-model="row.foreignTableName"
                  @blur="handleCellSave(row, $index, 'foreignTableName')"
                  @keyup.enter="handleCellSave(row, $index, 'foreignTableName')"
                  @keyup.esc="handleCellCancel(row, $index)"
                  placeholder=""
                />
              </div>
              <div v-else class="editable-cell" @dblclick="handleCellEdit(row, null, null, $index, 'foreignTableName')">
                <span v-if="row.foreignTableName" class="cell-content">{{ row.foreignTableName }}</span>
                <span v-else class="cell-placeholder">双击编辑</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="foreignColumnName" label="关联外键列名" min-width="150" align="center">
            <template #default="{ row, $index }">
              <div v-if="row.isEditing && editingCell.rowIndex === $index && editingCell.column === 'foreignColumnName'">
                <el-input
                  ref="editInputRef"
                  v-model="row.foreignColumnName"
                  @blur="handleCellSave(row, $index, 'foreignColumnName')"
                  @keyup.enter="handleCellSave(row, $index, 'foreignColumnName')"
                  @keyup.esc="handleCellCancel(row, $index)"
                  placeholder=""
                />
              </div>
              <div v-else class="editable-cell" @dblclick="handleCellEdit(row, null, null, $index, 'foreignColumnName')">
                <span v-if="row.foreignColumnName" class="cell-content">{{ row.foreignColumnName }}</span>
                <span v-else class="cell-placeholder">双击编辑</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { 
  Coin, 
  Folder, 
  Document, 
  Grid 
} from '@element-plus/icons-vue'
import { AxiosApiService } from './../../../chatagent/api/axios-api-service'
import Iconfont from '/@/components/Iconfont.vue';
// 树形数据相关
interface TreeNode {
  id: string
  label: string
  type: 'datasource' | 'database' | 'schema' | 'table'
  children?: TreeNode[]
  isLeaf?: boolean
  dataSourceId?: number
  dataSourceName?: string
  databaseType?: string
  databaseName?: string
  schemaName?: string
  tableName?: string
}

// 表格列数据
interface TableColumn {
  columnName: string
  originalComment?: string
  columnCommentAlias?: string
  functionExamples: string
  columnExampleData: string
  sortOrder?: number
  foreignTableName?: string
  foreignColumnName?: string
  isEditing?: boolean
  originalData?: any
}

// 编辑状态
interface EditingCell {
  rowIndex: number
  column: string
}

// 响应式数据
const treeRef = ref()
const tableRef = ref()
const editInputRef = ref()
const treeData = ref<TreeNode[]>([])
const tableColumns = ref<TableColumn[]>([])
const loading = ref(false)
const selectedTableName = ref('')
const editingCell = reactive<EditingCell>({ rowIndex: -1, column: '' })
const treeLoading = ref(false);
// 新增：表AI注释字段
const tableCommentAlias = ref('')
// 新增：同步状态跟踪
const isSyncing = ref(false)
// 面板宽度控制
const leftPanelWidth = ref(200)
const containerRef = ref<HTMLElement>()
const rightPanelWidth = computed(() => {
  if (containerRef.value) {
    return containerRef.value.clientWidth - leftPanelWidth.value - 6 // 6px 是分割线宽度
  }
  return window.innerWidth - leftPanelWidth.value - 6
})
const isResizing = ref(false)

// 同步AI注释到后端
const syncAnnotations = async () => {
  if (!selectedTableName.value) {
    ElMessage.warning('请先选择数据表')
    return
  }
  
  // 防止多次点击
  if (isSyncing.value) {
    ElMessage.warning('同步正在进行中，请稍候...')
    return
  }
  
  isSyncing.value = true
  
  try {
    const currentTableNode = getCurrentTableNode()
    if (!currentTableNode) {
      ElMessage.error('无法获取表信息')
      return
    }
    const syncParams = {
      dataSourceId: currentTableNode.dataSourceId!,
      databaseName: currentTableNode.databaseName!,
      schemaName: currentTableNode.schemaName!,
      tableName: currentTableNode.tableName!
      
    }
    // 调用同步API
    await AxiosApiService.syncDataAnnotations(syncParams)
    ElMessage.success('同步成功')
    // 可选：同步后刷新数据
    refreshAnnotations()
  } catch (error) {
    console.error('同步失败:', error)
    ElMessage.error('同步失败')
  } finally {
    // 同步完成后重置状态
    isSyncing.value = false
  }
}

// 节流函数
const throttle = (func: Function, delay: number) => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let lastExecTime = 0
  return function (this: any, ...args: any[]) {
    const currentTime = Date.now()

    if (currentTime - lastExecTime > delay) {
      func.apply(this, args)
      lastExecTime = currentTime
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      timeoutId = setTimeout(() => {
        func.apply(this, args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}

// 树形配置
const treeProps = {
  children: 'children',
  label: 'label',
  isLeaf: 'isLeaf'
}

// 计算是否有变更
const hasChanges = computed(() => {
  // 检查表注释是否有变动
  const tableAliasChanged = (() => {
    // 取当前表的原始注释
    const currentTableNodeVal = currentTableNode.value
    if (!currentTableNodeVal) return false
    // 取已保存的 tableCommentAlias
    // tableCommentAlias.value 是当前输入值
    // currentTableNodeVal._originalTableCommentAlias 由 loadTableColumns 时挂载
    return tableCommentAlias.value !== (currentTableNodeVal._originalTableCommentAlias || '')
  })()

  const columnsChanged = tableColumns.value.some(col => {
    const original = col.originalData
    return original && (
      col.columnCommentAlias !== original.columnCommentAlias ||
      col.functionExamples !== original.functionExamples ||
      col.columnExampleData !== original.columnExampleData ||
      col.foreignTableName !== original.foreignTableName ||
      col.foreignColumnName !== original.foreignColumnName
    )
  })
  return tableAliasChanged || columnsChanged
})

// 获取节点图标
const getNodeIcon = (type: string) => {
  switch (type) {
    case 'datasource':
      return Folder
    case 'database':
      return Coin
    case 'schema':
      return Folder
    case 'table':
      return Grid
    default:
      return Document
  }
}
const refreshTree = async () => {
  treeLoading.value = true;
  treeData.value = [];
  // 清除选中状态
  currentNodeKey.value = undefined;
  selectedTableName.value = '';
  currentTableNode.value = null;
  tableColumns.value = [];

  try {
    const dataSources = await AxiosApiService.getDataSourceList();
    treeData.value = dataSources.map(ds => ({
      id: `ds_${ds.id}`,
      label: ds.name,
      type: 'datasource',
      dataSourceId: ds.id,
      dataSourceName: ds.name,
      databaseType: ds.type,
      sqlType: ds.type,
      isLeaf: false
    }));
  } catch (error) {
    console.error('刷新数据源失败:', error);
  } finally {
    treeLoading.value = false;
  }
}
// 数据库类型列表（与数据源管理页面保持一致）
const databaseTypeList = [
  {
    label: 'MySQL',
    value: 'mysql',
    icon: 'mysql',
  },
  {
    label: 'H2',
    value: 'h2',
    icon: 'h2',
  },
  {
    label: 'Oracle',
    value: 'oracle',
    icon: 'oracle',
  },
  {
    label: 'PostgreSql',
    value: 'postgresql',
    icon: 'postgresql',
  },
  {
    label: 'SQLServer',
    icon: 'sqlserver',
    value: 'sqlserver',
  },
  {
    label: 'SQLite',
    icon: 'sqlite',
    value: 'sqlite',
  },
  {
    label: 'Mariadb',
    value: 'mariadb',
    icon: 'rds_mariadb',
  },
  {
    label: 'ClickHouse',
    value: 'clickhouse',
    icon: 'clickhouse-yunshujukuClickHouse',
  },
  {
    label: 'DM',
    value: 'dm',
    icon: 'dameng1',
  },
  {
    label: 'Presto',
    value: 'presto',
    icon: 'presto_sql',
  },
  {
    label: 'DB2',
    value: 'db2',
    icon: 'shujukuleixingtubiao-kuozhan-',
  },
  {
      label: 'Hive',
      icon: 'HIVE',
      value: 'hive',
    },
    {
      label: 'KingBase',
      icon: 'Kingbase',
      value: 'kingbase',
    },
    {
      label: 'MongoDB',
      icon: 'mongodb',
      value: 'mongodb',
    },

    {
      label: 'Timeplus',
      value: 'timeplus',
      icon: 'clickhouse-yunshujukuClickHouse',
    }
]

// 获取数据库类型对应的图标名称
const getDatabaseIcon = (sqlType: string) => {
  if (!sqlType) return null

  const lowerType = sqlType.toLowerCase()
  const foundItem = databaseTypeList.find((item) => item.value === lowerType)
  return foundItem ? foundItem.icon : null
}

// 公共表节点加载方法
const loadTableNodes = async (dataSourceId: number, databaseName: string, schemaName: string, dataSourceName: string, databaseType: string) => {
  const tables = await AxiosApiService.getTableList(dataSourceId, databaseName, schemaName)
  const tableList = Array.isArray(tables) ? tables : ((tables as any)?.data || [])
  return tableList.map((table: any) => ({
    id: `table_${dataSourceId}_${databaseName}_${schemaName}_${table.name}`,
    label: table.name,
    type: 'table' as const,
    dataSourceId,
    dataSourceName,
    databaseType,
    databaseName,
    schemaName,
    tableName: table.name,
    isLeaf: true
  }))
}

// 懒加载树节点
const loadNode = async (node: any, resolve: Function) => {
  const handleError = (msg: string, error: any) => {
    console.error(msg, error)
    if (node) node.loading = false
    resolve()
  }

  try {
    if (node.level === 0) {
      treeLoading.value = true
      const dataSources = await AxiosApiService.getDataSourceList()
      const nodes = dataSources.map(ds => ({
        id: `ds_${ds.id}`,
        label: ds.name,
        type: 'datasource' as const,
        dataSourceId: ds.id,
        dataSourceName: ds.name,
        databaseType: ds.type,
        sqlType: ds.type,
        isLeaf: false
      }))
      resolve(nodes)
      treeLoading.value = false
      return
    }

    const { type, dataSourceId, dataSourceName, databaseType, databaseName, schemaName } = node.data
    if (type === 'datasource') {
      const databases = await AxiosApiService.getDatabaseList(dataSourceId)
      const nodes = databases.map(db => ({
        id: `db_${dataSourceId}_${db.name}`,
        label: db.name,
        type: 'database' as const,
        dataSourceId,
        dataSourceName,
        databaseType,
        databaseName: db.name,
        isLeaf: false
      }))
      resolve(nodes)
      return
    }

    if (type === 'database') {
      const dbType = (databaseType || '').toUpperCase()
      if (["POSTGRESQL", "KINGBASE", "HIVE"].includes(dbType)) {
        const schemas = await AxiosApiService.getSchemaList(dataSourceId, databaseName)
        const nodes = schemas.map(schema => ({
          id: `schema_${dataSourceId}_${databaseName}_${schema.name}`,
          label: schema.name,
          type: 'schema' as const,
          dataSourceId,
          dataSourceName,
          databaseType,
          databaseName,
          schemaName: schema.name,
          isLeaf: false
        }))
        resolve(nodes)
        return
      }
      // 其他类型直接加载表，调用公共方法
      const nodes = await loadTableNodes(dataSourceId, databaseName, schemaName, dataSourceName, databaseType)
      resolve(nodes)
      return
    }

    if (type === 'schema') {
      // schema下统一调用公共方法
      const nodes = await loadTableNodes(dataSourceId, databaseName, schemaName, dataSourceName, databaseType)
      resolve(nodes)
      return
    }
  } catch (error) {
    if (node.level === 0) treeLoading.value = false
    if (node.data?.type === 'datasource') {
      handleError('加载数据库失败:', error)
    } else if (node.data?.type === 'database') {
      const dbType = (node.data.databaseType || '').toUpperCase()
      if (["POSTGRESQL", "KINGBASE", "HIVE"].includes(dbType)) {
        handleError('加载Schema失败:', error)
      } else {
        handleError('加载数据表失败:', error)
      }
    } else if (node.data?.type === 'schema') {
      handleError('加载数据表失败:', error)
    } else {
      handleError('加载数据源失败:', error)
    }
  }
}

// 处理节点点击
const handleNodeClick = async (data: TreeNode) => {
  // 设置当前选中的节点key，保持高亮状态
  currentNodeKey.value = data.id
  if (data.type === 'table') {
    selectedTableName.value = data.tableName || ''
    currentTableNode.value = data
    await loadTableColumns(data)
  }
}

// 加载表格列信息
const loadTableColumns = async (tableNode: TreeNode) => {
  loading.value = true
  try {
    // 调用获取表字段信息的API
    const columns = await AxiosApiService.getTableColumns(
      tableNode.dataSourceId!,
      tableNode.dataSourceName!,
      tableNode.databaseType!,
      tableNode.databaseName!,
      tableNode.schemaName!,
      tableNode.tableName!,
      1, // pageNo
      1000, // pageSize
      true
    )
    
    // 获取已保存的数据标注
    let savedAnnotations: any[] = []
    try {
      savedAnnotations = await AxiosApiService.getDataAnnotations(
        tableNode.dataSourceId!,
        tableNode.databaseName!,
        tableNode.schemaName!,
        tableNode.tableName!
      )
    } catch (error) {
      console.warn('获取数据标注失败，使用默认值:', error)
    }
    console.log('savedAnnotations',savedAnnotations)
  tableCommentAlias.value = savedAnnotations?.tableCommentAlias || ''
  // 保存原始表注释到当前表节点，供 hasChanges 对比
  tableNode._originalTableCommentAlias = savedAnnotations?.tableCommentAlias || ''
    // 创建标注映射
    const annotationMap = new Map()
    savedAnnotations?.columnAiMarkS?.forEach(annotation => {
      annotationMap.set(annotation.columnName, annotation)
    })

    tableColumns.value = columns.map((col, index) => {
      const savedAnnotation = annotationMap.get(col.columnName)
      const columnCommentAlias = savedAnnotation?.columnCommentAlias || ''
      const functionExamples = savedAnnotation?.functionExamples || ''
      const columnExampleData = savedAnnotation?.columnExampleData || ''
      const foreignTableName = savedAnnotation?.foreignTableName || ''
      const foreignColumnName = savedAnnotation?.foreignColumnName || ''

      return {
        columnName: col.columnName,
        originalComment: col.comment,
        columnCommentAlias,
        functionExamples,
        columnExampleData,
        foreignTableName,
        foreignColumnName,
        isEditing: false,
        originalData: {
          columnCommentAlias,
          functionExamples,
          columnExampleData,
          foreignTableName,
          foreignColumnName
        }
      }
    })
  } catch (error) {
    console.error('加载表字段失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理单元格双击编辑
const handleCellEdit = (row: TableColumn, _column: any, _cell: any, rowIndex: number, columnKey: string) => {
  const editableColumns = ['columnCommentAlias', 'functionExamples', 'columnExampleData', 'foreignTableName', 'foreignColumnName']
  if (editableColumns.includes(columnKey)) {
    editingCell.rowIndex = rowIndex
    editingCell.column = columnKey
    row.isEditing = true

    nextTick(() => {
      if (editInputRef.value) {
        editInputRef.value.focus()
      }
    })
  }
}

// 保存单元格编辑
const handleCellSave = (row: TableColumn, _rowIndex: number, _columnKey: string) => {
  row.isEditing = false
  editingCell.rowIndex = -1
  editingCell.column = ''
}

// 取消单元格编辑
const handleCellCancel = (row: TableColumn, _rowIndex: number) => {
  row.isEditing = false
  editingCell.rowIndex = -1
  editingCell.column = ''
  // 恢复原值
  if (row.originalData) {
    row.columnCommentAlias = row.originalData.columnCommentAlias
    row.functionExamples = row.originalData.functionExamples
    row.columnExampleData = row.originalData.columnExampleData
    row.foreignTableName = row.originalData.foreignTableName
    row.foreignColumnName = row.originalData.foreignColumnName
  }
}

// 保存标注
const saveAnnotations = async () => {
  if (!selectedTableName.value) {
    ElMessage.warning('请先选择数据表')
    return
  }

  try {
    // 获取当前选中的表节点信息
    const currentTableNode = getCurrentTableNode()
    if (!currentTableNode) {
      ElMessage.error('无法获取表信息')
      return
    }

    // 构建标注数据
    const columnAiMarkS = tableColumns.value.map(col => ({
      columnName: col.columnName,
      columnCommentAlias: col.columnCommentAlias,
      functionExamples: col.functionExamples,
      columnExampleData: col.columnExampleData,
      foreignTableName: col.foreignTableName,
      foreignColumnName: col.foreignColumnName,
      sortOrder: col.sortOrder
    }))

    // 组装接口参数结构
    const saveParams = {
      dataSourceId: currentTableNode.dataSourceId!,
      databaseName: currentTableNode.databaseName!,
      schemaName: currentTableNode.schemaName!,
      tableName: currentTableNode.tableName!,
      tableCommentAlias: tableCommentAlias.value,
      columnAiMarkS
    }

    // 调用保存API
    await AxiosApiService.saveDataAnnotations(saveParams)
    ElMessage.success('保存成功')

    // 更新原始数据
    tableColumns.value.forEach(col => {
      if (col.originalData) {
        col.originalData.columnCommentAlias = col.columnCommentAlias
        col.originalData.functionExamples = col.functionExamples
        col.originalData.columnExampleData = col.columnExampleData
        col.originalData.foreignTableName = col.foreignTableName
        col.originalData.foreignColumnName = col.foreignColumnName
      }
    })
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 获取当前表节点
const getCurrentTableNode = (): TreeNode | null => {
  // 这里需要保存当前选中的表节点信息
  // 可以通过遍历树或者保存在组件状态中
  return currentTableNode.value
}

// 当前选中的表节点
const currentTableNode = ref<TreeNode | null>(null)
// 当前选中的节点key，用于保持高亮状态
const currentNodeKey = ref<string | number | undefined>(undefined)

// 刷新标注
const refreshAnnotations = () => {
  if (selectedTableName.value && currentTableNode.value) {
    loadTableColumns(currentTableNode.value)
  }
}

// 处理调整大小的核心逻辑
const doResize = (e: MouseEvent) => {
  if (isResizing.value && containerRef.value) {
    const containerRect = containerRef.value.getBoundingClientRect()
    const newWidth = e.clientX - containerRect.left
    const minWidth = 200
    const maxWidth = containerRect.width - 400 // 保留右侧至少400px

    if (newWidth >= minWidth && newWidth <= maxWidth) {
      leftPanelWidth.value = newWidth
    }
  }
}

// 节流处理的 resize 函数
const handleResize = throttle(doResize, 16) // 约60fps

// 开始调整大小
const startResize = (e: MouseEvent) => {
  isResizing.value = true

  // 禁用文本选择
  document.body.style.userSelect = 'none'
  document.body.style.cursor = 'col-resize'

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  e.preventDefault()
}

// 停止调整大小
const stopResize = () => {
  isResizing.value = false

  // 恢复文本选择和光标
  document.body.style.userSelect = ''
  document.body.style.cursor = ''

  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}

// 窗口大小变化处理
const handleWindowResize = throttle(() => {
  if (containerRef.value) {
    const containerWidth = containerRef.value.clientWidth
    const maxLeftWidth = containerWidth - 400
    if (leftPanelWidth.value > maxLeftWidth) {
      leftPanelWidth.value = Math.max(200, maxLeftWidth)
    }
  }
}, 100)

// 组件挂载时添加窗口 resize 监听
onMounted(() => {
  window.addEventListener('resize', handleWindowResize)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  window.removeEventListener('resize', handleWindowResize)
})
</script>

<style scoped lang="scss">
.data-annotation-container {
  display: flex;
  height: calc(100vh - 200px);
  background-color: #f5f7fa;
}

.left-panel {
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  min-width: 200px;
  margin-left: -20px;
}

.right-panel {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 400px;
  overflow-y: auto;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.header-actions {
  display: flex;
  // gap: 8px;
}

.tree-container {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
}
.node-icon-iconfont{
  font-size: 14px;
  margin-right: 6px;
  color: var(--el-color-primary);
}
.tree-node {
  display: flex;
  align-items: center;
  
  .node-icon {
    margin-right: 8px;
    color: #606266;
  }
  
  .node-label {
    font-size: 14px;
    color: #303133;
  }
}

.splitter {
  width: 6px;
  background-color: #e6e6e6;
  cursor: col-resize;
  position: relative;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #409eff;
  }

  &:active {
    background-color: #337ecc;
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 20px;
    background-color: #fff;
    border-radius: 1px;
    transition: all 0.2s ease;
  }

  &:hover::after {
    height: 30px;
    width: 3px;
  }

  &.splitter-resizing {
    background-color: #337ecc;

    &::after {
      height: 40px;
      width: 4px;
      background-color: #fff;
      box-shadow: 0 0 4px rgba(51, 126, 204, 0.5);
    }
  }
}

.table-container {
  flex: 1;
  // padding: 16px;
}

.column-info {
  .column-name {
    font-weight: 600;
    color: #303133;
  }
  
  .column-comment {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
}

.editable-cell {
  min-height: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background-color: #f5f7fa;
  }

  .cell-content {
    color: #303133;
  }

  .cell-placeholder {
    color: #c0c4cc;
    font-style: italic;
  }
}

.sort-order {
  color: #606266;
  font-weight: 500;
}

:deep(.el-tree-node__content) {
  height: 36px;
}

// 鼠标悬停时的背景色
:deep(.el-tree-node__content:hover) {
  background-color: #ecf5ff !important;
}

// 当前选中节点的背景色（保持选中状态，即使失去焦点）
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff !important;
  color: #1890ff !important;
}

// 确保焦点状态下的选中节点背景色
:deep(.el-tree-node:focus > .el-tree-node__content) {
  background-color: #ecf5ff !important;
  color: #1890ff !important;
}

// 确保选中节点在失去焦点后仍保持背景色
:deep(.el-tree-node.is-current > .el-tree-node__content),
:deep(.el-tree-node.is-current:focus > .el-tree-node__content) {
  background-color: #ecf5ff !important;
  color: #1890ff !important;
}
// :deep(.el-table .cell) {
//   padding: 8px 12px;
// }

// :deep(.el-table th) {
//   background-color: #fafafa;
//   color: #303133;
//   font-weight: 600;
// }

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-table__body tr.current-row > td) {
  background-color: #ecf5ff;
}

:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #409eff;
}

:deep(.el-input__inner) {
  border-radius: 4px;
}

.panel-header .header-actions .el-button {
  border-radius: 4px;
}

.panel-header .header-actions .el-button--primary {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.panel-header .header-actions .el-button--primary:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}
</style>
