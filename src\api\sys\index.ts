/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
import { defHttp } from '/@/utils/axios';
import { LoginParams, PhoneLoginParams } from '/@/api/model/User';
import { UserReturn } from '/#/store';
enum Api {
  SESSION_TIMEOUT = '/user/sessionTimeout',
  TOKEN_EXPIRED = '/user/tokenExpired',
  // LOGIN = '/_user/login',
  // LOGIN = '/oauth/token',
  GetMenuList = '/sys/user/getRouters',
  LOGIN = '/sys/user/login',
  USER_INFO = '/sys/user/info',
  // USER_INFO = '/sys/user/info',

  Logout = '/sys/system/user/logout',
  LogoutCode = '/certification/code',
  CrtLogin = '/sso/crtLogin',
  // PhoneMsgCode = '/msg/code',
  PhoneMsgCode = '/sso/smsCode',
  // PhoneLogin = '/sso/phoneLogin',
  PhoneLogin = '/sso/doLogin',
  CodeUrl = '/sys/user/forget/pass',
  findPwd = '/sys/user/change/pass',
  valCode = '/sys/user/validate/code',
  imgUrl = '/sys/oss/login/image',
  // dictCode = '/sys/dict/code',
  Artifact_USER_INFO = '/sso/checkArtifact',
  batchConfirmation = '/sys/user/confirm',
  register = '/sys/user/register',
  asyncDeptAll = '/sso/asyncDeptAll',
  dictCode = '/sys/dict/code',
  checkUrl = '/sys/employee/check',
  checkUser = '/sys/user/check',
  myNotices = '/sys/notice/myNotices',
  prompt = '/sys/notice/prompt',
  getNumber = '/sys/notice/getNumber',
  getNoticeId = '/sys/notice/',
}

// Get personal center-basic settings sys/user/confirm

export const userLogin = (params: LoginParams) =>
  defHttp.post({ url: Api.LOGIN, params });
export const getMenus = () => {
  return defHttp.get({ url: Api.GetMenuList });
};

/**
 * 登录获取用户信息
 */
export const getUserInfoLogin = () => {
  return defHttp.get<UserReturn>({ url: Api.USER_INFO });
};
/**
 * 刷新用户信息
 */
export const getUserInfo = () => {
  return defHttp.get<UserReturn>({ url: Api.USER_INFO });
};

export const sessionTimeoutApi = () => defHttp.post<void>({ url: Api.SESSION_TIMEOUT });

export const tokenExpiredApi = () => defHttp.post<void>({ url: Api.TOKEN_EXPIRED });

export const userLogout = () => defHttp.get({ url: Api.Logout });

export const getLoginCode = () => defHttp.post({ url: Api.LogoutCode });

export const getCrtLogin = (params) => defHttp.post({ url: Api.CrtLogin, params });
/**
 * 使用统一认证页面登录，获取用户信息
 */
export const getArtifactUserInfoLogin = (artifact: string) => {
  return defHttp.get({
    url: `${Api.Artifact_USER_INFO}?artifact=${artifact}`,
  });
};

// 获取短信验证码
export const getMsgCode = (phone: string) =>
  defHttp.get({ url: `${Api.PhoneMsgCode}?phone=${phone}` });
// 短信登录
export const phoneLogin = (params: PhoneLoginParams) =>
  defHttp.post({ url: Api.PhoneLogin, params });

export const getCodeApi = (params) => {
  return defHttp.get({ url: Api.CodeUrl, params });
};
export const getfindPwdApi = (params) => {
  return defHttp.get({ url: Api.findPwd, params });
};
export const getValCode = (params) => {
  return defHttp.get({ url: Api.valCode, params });
};

export const getImgApi = () => {
  return defHttp.get({ url: Api.imgUrl });
};

export const getdictCode = (params) => {
  return defHttp.get({ url: Api.dictCode, params });
};

export const getBatchConfirmation = (params) =>
  defHttp.post({ url: Api.batchConfirmation, params });
// valCode
// CodeUrl
export const postRegister = (params) => defHttp.post({ url: Api.register, params });

export const asyncDeptAll = (params) =>
  defHttp.get({
    url: `${Api.asyncDeptAll}?id=${params.id}&name=${params.name}&url=66a452688527d58af09b894eeeb606a0`,
  });

export const dictCode = (code) =>
  defHttp.get({
    url: `${Api.dictCode}?code=${code}&url=2499966da9a73adf6dc98f04536c917ad6f3a81a2758c2e6cefe0623541b6b3a`,
  });

export const checkApi = (params) => defHttp.get({ url: Api.checkUrl, params });

export const checkUser = (params) => defHttp.get({ url: Api.checkUser, params });

export const myNotices = (params) => defHttp.get({ url: Api.myNotices, params });

export const prompt = (params) => defHttp.get({ url: Api.prompt, params });

export const getNumber = () => defHttp.get({ url: Api.getNumber });

export const getNoticeId = (id) => defHttp.get({ url: `${Api.getNoticeId}${id}` });
